<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #cart-display { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Cart Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test Add to Cart</h2>
        <button onclick="testAddToCart(1)">Add Product 1</button>
        <button onclick="testAddToCart(2)">Add Product 2</button>
        <button onclick="testAddToCart(3)">Add Product 3</button>
    </div>
    
    <div class="test-section">
        <h2>Cart Display</h2>
        <div id="cart-display"></div>
        <button onclick="displayCart()">Show Cart Contents</button>
        <button onclick="clearCart()">Clear Cart</button>
    </div>
    
    <div class="test-section">
        <h2>Test Remove from Cart</h2>
        <button onclick="testRemoveFromCart(1)">Remove Product 1</button>
        <button onclick="testRemoveFromCart(2)">Remove Product 2</button>
    </div>

    <script>
        // Sample products for testing
        const sampleProducts = {
            1: { name: "Fresh Orange", price: 12.99, image: "image/product-1.png" },
            2: { name: "Fresh Onion", price: 15.99, image: "image/product-2.png" },
            3: { name: "Fresh Meat", price: 20.99, image: "image/product-3.png" }
        };

        // Cart array
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Test add to cart function
        function testAddToCart(productId) {
            const product = sampleProducts[productId];
            if (!product) {
                console.log('Product not found!');
                return;
            }

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: productId,
                    name: product.name,
                    price: product.price,
                    image: product.image,
                    quantity: 1,
                    addedAt: new Date().toISOString()
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            console.log(`${product.name} added to cart!`);
            displayCart();
        }

        // Test remove from cart function
        function testRemoveFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            localStorage.setItem('cart', JSON.stringify(cart));
            console.log(`Product ${productId} removed from cart!`);
            displayCart();
        }

        // Display cart contents
        function displayCart() {
            const cartDisplay = document.getElementById('cart-display');
            if (cart.length === 0) {
                cartDisplay.innerHTML = '<p>Cart is empty</p>';
                return;
            }

            let html = '<h3>Cart Contents:</h3>';
            let total = 0;
            
            cart.forEach(item => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                html += `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0;">
                        <strong>${item.name}</strong><br>
                        Price: $${item.price.toFixed(2)}<br>
                        Quantity: ${item.quantity}<br>
                        Subtotal: $${itemTotal.toFixed(2)}
                    </div>
                `;
            });
            
            html += `<h4>Total: $${total.toFixed(2)}</h4>`;
            cartDisplay.innerHTML = html;
        }

        // Clear cart
        function clearCart() {
            cart = [];
            localStorage.removeItem('cart');
            displayCart();
            console.log('Cart cleared!');
        }

        // Initialize display
        displayCart();
    </script>
</body>
</html>
